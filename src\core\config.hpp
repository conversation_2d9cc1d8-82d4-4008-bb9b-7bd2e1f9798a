#pragma once

#include <string>
#include <unordered_map>
#include <filesystem>
#include <fstream>
#include <nlohmann/json.hpp>
#include <windows.h>
#include "../utils/output.hpp"

namespace sco {

class Config {
public:
    static Config& instance() {
        static Config instance;
        return instance;
    }
    
    // Configuration management
    void load() {
        auto config_file = get_config_file();

        output::debug("Loading configuration from: " + config_file.string());

        if (!std::filesystem::exists(config_file)) {
            output::debug("Configuration file not found, creating with defaults");
            set_defaults();
            save();
            return;
        }

        try {
            std::ifstream file(config_file);
            if (file.is_open()) {
                file >> config_data_;
                output::debug("Configuration loaded successfully");

                // Ensure root_path exists, set default if not present
                if (!config_data_.contains("root_path") || config_data_["root_path"].get<std::string>().empty()) {
                    std::filesystem::path default_root = global_mode_ ?
                        (get_program_data() / "scoop") :
                        (get_user_profile() / "scoop");
                    config_data_["root_path"] = default_root.string();
                    save(); // Save updated configuration
                }
            }
        } catch (const std::exception& e) {
            output::warn("Failed to load configuration: " + std::string(e.what()));
            set_defaults();
        }
    }
    
    void save() {
        auto config_file = get_config_file();
        
        try {
            // Ensure directory exists
            std::filesystem::create_directories(config_file.parent_path());
            
            std::ofstream file(config_file);
            if (file.is_open()) {
                file << config_data_.dump(2);
                output::debug("Configuration saved to: " + config_file.string());
            }
        } catch (const std::exception& e) {
            output::error("Failed to save configuration: " + std::string(e.what()));
        }
    }
    
    // Getters
    std::string get(const std::string& key, const std::string& default_value = "") const {
        if (config_data_.contains(key)) {
            return config_data_[key].get<std::string>();
        }
        return default_value;
    }
    
    bool get_bool(const std::string& key, bool default_value = false) const {
        if (config_data_.contains(key)) {
            return config_data_[key].get<bool>();
        }
        return default_value;
    }
    
    int get_int(const std::string& key, int default_value = 0) const {
        if (config_data_.contains(key)) {
            return config_data_[key].get<int>();
        }
        return default_value;
    }
    
    // Setters
    void set(const std::string& key, const std::string& value) {
        config_data_[key] = value;
    }
    
    void set_bool(const std::string& key, bool value) {
        config_data_[key] = value;
    }
    
    void set_int(const std::string& key, int value) {
        config_data_[key] = value;
    }
    
    // Global mode
    void set_global_mode(bool global) { global_mode_ = global; }
    bool is_global_mode() const { return global_mode_; }
    
    // Paths
    std::filesystem::path get_scoop_dir() const {
        // First try to read root_path from configuration file
        std::string root_path = get("root_path");
        if (!root_path.empty()) {
            return std::filesystem::path(root_path);
        }

        // If root_path is not configured, use default path
        if (global_mode_) {
            return get_program_data() / "scoop";
        } else {
            return get_user_profile() / "scoop";
        }
    }
    
    std::filesystem::path get_apps_dir() const {
        return get_scoop_dir() / "apps";
    }

    std::filesystem::path get_global_apps_dir() const {
        return get_program_data() / "scoop" / "apps";
    }

    std::filesystem::path get_cache_dir() const {
        return get_scoop_dir() / "cache";
    }
    
    std::filesystem::path get_buckets_dir() const {
        return get_scoop_dir() / "buckets";
    }
    
    std::filesystem::path get_shims_dir() const {
        return get_scoop_dir() / "shims";
    }
    
    std::filesystem::path get_config_file() const {
        // Scoop configuration file is located at %USERPROFILE%\.config\scoop\config.json
        return get_user_profile() / ".config" / "scoop" / "config.json";
    }
    
    // Get the actual root path for Scoop installation
    std::filesystem::path get_root_path() const {
        std::string root_path = get("root_path");
        if (!root_path.empty()) {
            return std::filesystem::path(root_path);
        }

        // Default path
        if (global_mode_) {
            return get_program_data() / "scoop";
        } else {
            return get_user_profile() / "scoop";
        }
    }

    // Default configuration values
    void set_defaults() {
        // Set default root_path
        std::filesystem::path default_root = global_mode_ ?
            (get_program_data() / "scoop") :
            (get_user_profile() / "scoop");

        config_data_ = nlohmann::json{
            {"root_path", default_root.string()},
            {"aria2-enabled", true},
            {"aria2-warning-enabled", true},
            {"aria2-retry-wait", 2},
            {"aria2-split", 5},
            {"aria2-max-connection-per-server", 5},
            {"aria2-min-split-size", "5M"},
            {"aria2-options", ""},
            {"debug", false},
            {"force_update", false},
            {"show_update_log", true},
            {"scoop_repo", "https://github.com/ScoopInstaller/Scoop"},
            {"scoop_branch", "master"}
        };
    }

private:
    Config() = default;
    ~Config() = default;
    Config(const Config&) = delete;
    Config& operator=(const Config&) = delete;
    
    nlohmann::json config_data_;
    bool global_mode_ = false;
    
    std::filesystem::path get_user_profile() const {
        char* userprofile = nullptr;
        size_t len = 0;
        if (_dupenv_s(&userprofile, &len, "USERPROFILE") == 0 && userprofile != nullptr) {
            std::filesystem::path path(userprofile);
            free(userprofile);
            return path;
        }
        return std::filesystem::current_path();
    }
    
    std::filesystem::path get_program_data() const {
        char* programdata = nullptr;
        size_t len = 0;
        if (_dupenv_s(&programdata, &len, "PROGRAMDATA") == 0 && programdata != nullptr) {
            std::filesystem::path path(programdata);
            free(programdata);
            return path;
        }
        return std::filesystem::path("C:\\ProgramData");
    }
};

} // namespace sco
