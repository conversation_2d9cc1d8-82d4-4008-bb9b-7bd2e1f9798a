#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include <iostream>
#include "../utils/output.hpp"

namespace sco {

class ConfigCommand : public BaseCommand {
public:
    ConfigCommand() = default;
    
    int execute() override {
        try {
            auto& config = Config::instance();
            config.load();
            
            if (key_.empty()) {
                // Show all configuration
                show_all_config();
            } else if (value_.empty()) {
                // Get specific configuration
                show_config_value(key_);
            } else {
                // Set configuration
                set_config_value(key_, value_);
            }
            
            return 0;
        } catch (const std::exception& e) {
            output::error("Config command failed: {}", e.what());
            return 1;
        }
    }
    
    std::string get_name() const override { return "config"; }
    std::string get_description() const override { return "Get or set configuration values"; }
    
    void set_key(const std::string& key) { key_ = key; }
    void set_value(const std::string& value) { value_ = value; }
    
private:
    std::string key_;
    std::string value_;
    
    void show_all_config() {
        auto& config = Config::instance();

        std::cout << "Current configuration:\n";
        std::cout << "  root_path: " << config.get("root_path") << "\n";
        std::cout << "  aria2-enabled: " << (config.get_bool("aria2-enabled") ? "true" : "false") << "\n";
        std::cout << "  aria2-warning-enabled: " << (config.get_bool("aria2-warning-enabled") ? "true" : "false") << "\n";
        std::cout << "  aria2-retry-wait: " << config.get_int("aria2-retry-wait") << "\n";
        std::cout << "  aria2-split: " << config.get_int("aria2-split") << "\n";
        std::cout << "  aria2-max-connection-per-server: " << config.get_int("aria2-max-connection-per-server") << "\n";
        std::cout << "  aria2-min-split-size: " << config.get("aria2-min-split-size") << "\n";
        std::cout << "  aria2-options: " << config.get("aria2-options") << "\n";
        std::cout << "  debug: " << (config.get_bool("debug") ? "true" : "false") << "\n";
        std::cout << "  force_update: " << (config.get_bool("force_update") ? "true" : "false") << "\n";
        std::cout << "  show_update_log: " << (config.get_bool("show_update_log") ? "true" : "false") << "\n";
        std::cout << "  scoop_repo: " << config.get("scoop_repo") << "\n";
        std::cout << "  scoop_branch: " << config.get("scoop_branch") << "\n";

        std::cout << "\nPaths:\n";
        std::cout << "  root_path: " << config.get_root_path().string() << "\n";
        std::cout << "  config_file: " << config.get_config_file().string() << "\n";
        std::cout << "  apps_dir: " << config.get_apps_dir().string() << "\n";
        std::cout << "  cache_dir: " << config.get_cache_dir().string() << "\n";
        std::cout << "  buckets_dir: " << config.get_buckets_dir().string() << "\n";
        std::cout << "  shims_dir: " << config.get_shims_dir().string() << "\n";

        output::debug("Configuration displayed successfully");
    }
    
    void show_config_value(const std::string& key) {
        auto& config = Config::instance();

        output::debug("Getting config value for key: {}", key);

        // Check if it's a boolean config
        if (key == "aria2-enabled" || key == "aria2-warning-enabled" ||
            key == "debug" || key == "force_update" || key == "show_update_log") {
            std::cout << (config.get_bool(key) ? "true" : "false") << "\n";
        }
        // Check if it's an integer config
        else if (key == "aria2-retry-wait" || key == "aria2-split" ||
                 key == "aria2-max-connection-per-server") {
            std::cout << config.get_int(key) << "\n";
        }
        // String config
        else {
            std::string value = config.get(key);
            if (value.empty()) {
                // 特殊处理 root_path，如果为空则显示默认值
                if (key == "root_path") {
                    std::cout << config.get_root_path().string() << "\n";
                } else {
                    std::cerr << "Configuration key '" << key << "' not found.\n";
                    output::warn("Configuration key '{}' not found", key);
                }
                return;
            }
            std::cout << value << "\n";
        }
    }
    
    void set_config_value(const std::string& key, const std::string& value) {
        auto& config = Config::instance();

        output::debug("Setting config: {} = {}", key, value);

        // Check if it's a boolean config
        if (key == "aria2-enabled" || key == "aria2-warning-enabled" ||
            key == "debug" || key == "force_update" || key == "show_update_log") {
            bool bool_value = (value == "true" || value == "1" || value == "yes");
            config.set_bool(key, bool_value);
            std::cout << "Set " << key << " to " << (bool_value ? "true" : "false") << "\n";
        }
        // Check if it's an integer config
        else if (key == "aria2-retry-wait" || key == "aria2-split" ||
                 key == "aria2-max-connection-per-server") {
            try {
                int int_value = std::stoi(value);
                config.set_int(key, int_value);
                std::cout << "Set " << key << " to " << int_value << "\n";
            } catch (const std::exception&) {
                std::cerr << "Invalid integer value: " << value << "\n";
                output::error("Invalid integer value: {}", value);
                return;
            }
        }
        // String config
        else {
            // 特殊处理 root_path，验证路径有效性
            if (key == "root_path") {
                std::filesystem::path path(value);
                if (!path.is_absolute()) {
                    std::cerr << "root_path must be an absolute path: " << value << "\n";
                    output::error("Invalid root_path (not absolute): {}", value);
                    return;
                }
                // 确保目录存在
                try {
                    std::filesystem::create_directories(path);
                } catch (const std::exception& e) {
                    std::cerr << "Failed to create root_path directory: " << e.what() << "\n";
                    output::error("Failed to create root_path directory: {}", e.what());
                    return;
                }
            }

            config.set(key, value);
            std::cout << "Set " << key << " to " << value << "\n";
        }

        config.save();
        output::info("Configuration updated: {} = {}", key, value);
    }
};

} // namespace sco
