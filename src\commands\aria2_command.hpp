#pragma once

#include "base_command.hpp"
#include "../utils/download_manager.hpp"
#include "../core/config.hpp"
#include <iostream>

namespace sco {

class Aria2Command : public BaseCommand {
public:
    int execute() override {
        auto& config = Config::instance();
        
        std::cout << "aria2 Status:\n";
        std::cout << "=============\n\n";
        
        // Check if aria2 is enabled in config
        bool enabled = config.get_bool("aria2-enabled");
        std::cout << "Enabled in config: " << (enabled ? "Yes" : "No") << "\n";
        
        // Check if aria2c is available
        bool available = Aria2Downloader::is_aria2_available();
        std::cout << "aria2c available: " << (available ? "Yes" : "No") << "\n";
        
        if (!available) {
            std::cout << "\nTo install aria2, run:\n";
            std::cout << "  scoop install aria2\n";
        }
        
        std::cout << "\nCurrent aria2 configuration:\n";
        std::cout << "  aria2-enabled: " << (config.get_bool("aria2-enabled") ? "true" : "false") << "\n";
        std::cout << "  aria2-warning-enabled: " << (config.get_bool("aria2-warning-enabled") ? "true" : "false") << "\n";
        std::cout << "  aria2-retry-wait: " << config.get_int("aria2-retry-wait") << "\n";
        std::cout << "  aria2-split: " << config.get_int("aria2-split") << "\n";
        std::cout << "  aria2-max-connection-per-server: " << config.get_int("aria2-max-connection-per-server") << "\n";
        std::cout << "  aria2-min-split-size: " << config.get("aria2-min-split-size") << "\n";
        std::cout << "  aria2-options: " << config.get("aria2-options") << "\n";
        
        if (enabled && available) {
            std::cout << "\n✓ aria2 is properly configured and ready to use.\n";
        } else if (enabled && !available) {
            std::cout << "\n⚠ aria2 is enabled but not installed. Downloads will use built-in downloader.\n";
        } else {
            std::cout << "\nℹ aria2 is disabled. Downloads will use built-in downloader.\n";
        }

        return 0;
    }

    std::string get_name() const override {
        return "aria2";
    }

    std::string get_description() const override {
        return "Show aria2 status and configuration";
    }
};

} // namespace sco
