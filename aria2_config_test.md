# aria2 Configuration Test

This document describes the new aria2 configuration behavior and how to test it.

## New Behavior

1. **aria2 is disabled by default** - `aria2-enabled` defaults to `false`
2. **No aria2 config in default config file** - aria2 settings only appear in config.json when explicitly enabled
3. **Default values provided programmatically** - when aria2 is disabled, default values are returned without storing them

## Default Values

When aria2 is disabled (default state):
- `aria2-enabled`: `false`
- `aria2-warning-enabled`: `true` 
- `aria2-retry-wait`: `2`
- `aria2-split`: `5`
- `aria2-max-connection-per-server`: `5`
- `aria2-min-split-size`: `"5M"`
- `aria2-options`: `""`

## Testing Steps

### 1. Fresh Installation (Default State)

```bash
# Check aria2 status - should show disabled
sco aria2

# Check config - should NOT contain aria2 settings
sco config

# Check individual aria2 settings - should show default values
sco config aria2-enabled        # Should show: false
sco config aria2-retry-wait     # Should show: 2
sco config aria2-split          # Should show: 5
```

Expected config.json content (no aria2 settings):
```json
{
  "root_path": "C:\\Users\\<USER>\\scoop",
  "debug": false,
  "force_update": false,
  "show_update_log": true,
  "scoop_repo": "https://github.com/ScoopInstaller/Scoop",
  "scoop_branch": "master"
}
```

### 2. Enable aria2

```bash
# Enable aria2 - should add all aria2 settings to config
sco config aria2-enabled true

# Check config - should now contain all aria2 settings
sco config

# Check aria2 status - should show enabled
sco aria2
```

Expected config.json content after enabling:
```json
{
  "root_path": "C:\\Users\\<USER>\\scoop",
  "debug": false,
  "force_update": false,
  "show_update_log": true,
  "scoop_repo": "https://github.com/ScoopInstaller/Scoop",
  "scoop_branch": "master",
  "aria2-enabled": true,
  "aria2-warning-enabled": true,
  "aria2-retry-wait": 2,
  "aria2-split": 5,
  "aria2-max-connection-per-server": 5,
  "aria2-min-split-size": "5M",
  "aria2-options": ""
}
```

### 3. Modify aria2 Settings

```bash
# Modify individual settings
sco config aria2-split 10
sco config aria2-retry-wait 5

# Check that changes are saved
sco config aria2-split          # Should show: 10
sco config aria2-retry-wait     # Should show: 5
```

### 4. Disable aria2

```bash
# Disable aria2
sco config aria2-enabled false

# Check status - should show disabled but keep other settings in config
sco aria2

# Check config - aria2-enabled should be false, other settings remain
sco config
```

## Implementation Details

### Config Class Changes

1. **Modified getter methods** to check for aria2 defaults when keys are not in config
2. **Added `enable_aria2()` method** that sets all aria2 configuration at once
3. **Removed aria2 settings from `set_defaults()`** 
4. **Added private helper methods** for aria2 default values

### ConfigCommand Changes

1. **Special handling for `aria2-enabled`** - calls `enable_aria2()` when set to true
2. **Fixed Chinese comments** to use English

### Aria2Command Changes

1. **Enhanced output** to show when values are defaults vs configured
2. **Added helpful messages** about enabling aria2

This ensures that:
- Fresh installations have clean config files without aria2 clutter
- aria2 is opt-in rather than enabled by default
- Default values are still available when needed
- Once enabled, all aria2 settings are properly stored and configurable
